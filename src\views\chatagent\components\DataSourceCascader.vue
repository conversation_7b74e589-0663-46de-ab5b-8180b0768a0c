<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="datasource-cascader">
    <!-- 选择框 -->
    <div class="cascader-input" @click="toggleDropdown" :class="{ active: showDropdown }">
      <span class="cascader-value" v-if="displayValue">{{ displayValue }}</span>
      <span class="cascader-placeholder" v-else>请选择数据源</span>
      <div class="cascader-actions">
        <Icon
          v-if="displayValue"
          icon="carbon:close"
          class="clear-btn"
          @click.stop="clearSelection"
          title="清除选择"
        />
        <Icon icon="carbon:chevron-down" class="cascader-arrow" :class="{ rotated: showDropdown }" />
      </div>
    </div>

    <!-- 下拉面板 -->
    <div class="cascader-dropdown" v-if="showDropdown" @click.stop>
      <div class="cascader-panels">
        <!-- 数据源面板 -->
        <div class="cascader-panel">
          <div class="panel-header">
            <Icon icon="carbon:data-base" />
            <span>数据源</span>
          </div>
          <div class="panel-content">
            <div v-if="dataSourceLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            
            <div 
              v-else
              v-for="dataSource in dataSourceList" 
              :key="dataSource.id"
              class="cascader-item"
              :class="{ active: selectedDataSource?.id === dataSource.id }"
              @click="selectDataSource(dataSource)"
            >
              <Icon icon="carbon:data-base" />
              <span>{{ dataSource.name }}</span>
              <Icon icon="carbon:chevron-right" class="item-arrow" />
            </div>
          </div>
        </div>

        <!-- 数据库面板 -->
        <div class="cascader-panel" v-if="selectedDataSource">
          <div class="panel-header">
            <Icon icon="carbon:data-table" />
            <span>数据库</span>
          </div>
          <div class="panel-content">
            <div v-if="databaseLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            <div
              v-else
              v-for="database in databaseList"
              :key="database.name"
              class="cascader-item"
              :class="{ active: selectedDatabase?.name === database.name }"
              @click="selectDatabase(database)"
            >
              <Icon icon="carbon:data-table" />
              <span>{{ database.name }}</span>
              <Icon
                v-if="needsSchemaSelection"
                icon="carbon:chevron-right"
                class="item-arrow"
              />
            </div>
          </div>
        </div>

        <!-- 模式面板 (只有POSTGRESQL和KINGBASE类型才显示) -->
        <div class="cascader-panel" v-if="selectedDatabase && needsSchemaSelection">
          <div class="panel-header">
            <Icon icon="carbon:table" />
            <span>模式</span>
          </div>
          <div class="panel-content">
            <div v-if="schemaLoading" class="loading-item">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>加载中...</span>
            </div>
            <div
              v-else
              v-for="schema in schemaList"
              :key="schema.name"
              class="cascader-item"
              :class="{ active: selectedSchema?.name === schema.name }"
              @click="selectSchema(schema)"
            >
              <Icon icon="carbon:table" />
              <span>{{ schema.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { DataSourceApiService, type DataSource, type Database, type Schema } from '../api/datasource-api-service'
import { AxiosApiService } from '../api/axios-api-service'

// Props
interface Props {
  modelValue?: {
    dataSourceId?: number
    dataSourceName?: string
    databaseName?: string
    schemaName?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: {
    dataSourceId?: number
    dataSourceName?: string
    databaseName?: string
    schemaName?: string
  }]
}>()

// 响应式数据
const showDropdown = ref(false)
const dataSourceList = ref<DataSource[]>([])
const databaseList = ref<Database[]>([])
const schemaList = ref<Schema[]>([])

const selectedDataSource = ref<DataSource | null>(null)
const selectedDatabase = ref<Database | null>(null)
const selectedSchema = ref<Schema | null>(null)

const dataSourceLoading = ref(false)
const databaseLoading = ref(false)
const schemaLoading = ref(false)

// 计算属性
const displayValue = computed(() => {
  if (selectedDataSource.value && selectedDatabase.value && selectedSchema.value) {
    return `${selectedDataSource.value.name}/${selectedDatabase.value.name}/${selectedSchema.value.name}`
  }
  if (selectedDataSource.value && selectedDatabase.value && !needsSchemaSelection.value) {
    return `${selectedDataSource.value.name}/${selectedDatabase.value.name}`
  }
  return ''
})

// 判断是否需要选择模式（只有POSTGRESQL和KINGBASE类型需要选择模式）
const needsSchemaSelection = computed(() => {
  if (!selectedDataSource.value) return false
  const type = selectedDataSource.value.type?.toUpperCase()
  return type === 'POSTGRESQL' || type === 'KINGBASE' || type === 'HIVE'
})

// 方法
const toggleDropdown = async () => {
  showDropdown.value = !showDropdown.value
  if (showDropdown.value && dataSourceList.value.length === 0) {
    await loadDataSources()
  }
}

const loadDataSources = async () => {
  dataSourceLoading.value = true
  try {
    // dataSourceList.value = await DataSourceApiService.getDataSourceList()
    dataSourceList.value = await AxiosApiService.getDataSourceList()
  } catch (error) {
    console.error('加载数据源失败:', error)
  } finally {
    dataSourceLoading.value = false
  }
}

const selectDataSource = async (dataSource: DataSource) => {
  selectedDataSource.value = dataSource
  selectedDatabase.value = null
  selectedSchema.value = null
  databaseList.value = []
  schemaList.value = []

  // 加载数据库列表
  databaseLoading.value = true
  try {
    databaseList.value = await AxiosApiService.getDatabaseList(dataSource.id)
  } catch (error) {
    console.error('加载数据库失败:', error)
  } finally {
    databaseLoading.value = false
  }
}

const selectDatabase = async (database: Database) => {
  selectedDatabase.value = database
  selectedSchema.value = null
  schemaList.value = []

  // 如果不需要选择模式，直接完成选择
  if (!needsSchemaSelection.value) {
    const value = {
      dataSourceId: selectedDataSource.value?.id,
      dataSourceName: selectedDataSource.value?.name,
      databaseName: database.name,
      schemaName: undefined
    }

    emit('update:modelValue', value)
    showDropdown.value = false
    return
  }

  // 需要选择模式的情况下，加载模式列表
  if (selectedDataSource.value) {
    schemaLoading.value = true
    try {
      schemaList.value = await AxiosApiService.getSchemaList(
        selectedDataSource.value.id,
        database.name
      )
    } catch (error) {
      console.error('加载模式失败:', error)
    } finally {
      schemaLoading.value = false
    }
  }
}

const selectSchema = (schema: Schema) => {
  selectedSchema.value = schema

  // 发出选择完成事件
  const value = {
    dataSourceId: selectedDataSource.value?.id,
    dataSourceName: selectedDataSource.value?.name,
    databaseName: selectedDatabase.value?.name,
    schemaName: schema.name
  }

  emit('update:modelValue', value)

  // 隐藏下拉框
  showDropdown.value = false
}

const clearSelection = () => {
  selectedDataSource.value = null
  selectedDatabase.value = null
  selectedSchema.value = null
  databaseList.value = []
  schemaList.value = []

  // 发出清空事件
  emit('update:modelValue', {})
}

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.datasource-cascader')) {
    showDropdown.value = false
  }
}

// 监听外部传入的modelValue变化，用于回显选择
watch(() => props.modelValue, async (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    console.log('🔄 DataSourceCascader 接收到外部值变化:', newValue)

    // 如果有dataSourceId，需要先加载数据源列表
    if (newValue.dataSourceId && dataSourceList.value.length === 0) {
      await loadDataSources()
    }

    // 查找并设置数据源
    if (newValue.dataSourceId) {
      const dataSource = dataSourceList.value.find(ds => ds.id === newValue.dataSourceId)
      if (dataSource) {
        selectedDataSource.value = dataSource
        console.log('✅ 找到并设置数据源:', dataSource)

        // 加载数据库列表
        if (newValue.databaseName) {
          try {
            databaseLoading.value = true
            databaseList.value = await AxiosApiService.getDatabaseList(dataSource.id)

            // 查找并设置数据库
            const database = databaseList.value.find(db => db.name === newValue.databaseName)
            if (database) {
              selectedDatabase.value = database
              console.log('✅ 找到并设置数据库:', database)

              // 如果需要选择模式且有schemaName
              if (needsSchemaSelection.value && newValue.schemaName) {
                try {
                  schemaLoading.value = true
                  schemaList.value = await AxiosApiService.getSchemaList(dataSource.id, database.name)

                  // 查找并设置模式
                  const schema = schemaList.value.find(s => s.name === newValue.schemaName)
                  if (schema) {
                    selectedSchema.value = schema
                    console.log('✅ 找到并设置模式:', schema)
                  } else {
                    console.warn('⚠️ 未找到指定的模式:', newValue.schemaName)
                  }
                } catch (error) {
                  console.error('❌ 回显时加载模式失败:', error)
                } finally {
                  schemaLoading.value = false
                }
              }
            } else {
              console.warn('⚠️ 未找到指定的数据库:', newValue.databaseName)
            }
          } catch (error) {
            console.error('❌ 回显时加载数据库失败:', error)
          } finally {
            databaseLoading.value = false
          }
        }
      } else {
        console.warn('⚠️ 未找到指定的数据源:', newValue.dataSourceId)
      }
    }
  } else {
    // 如果传入空值，清空选择
    clearSelection()
  }
}, { immediate: true, deep: true })

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.datasource-cascader {
  position: relative;
  width: 100%;
}

.cascader-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
  width: fit-content;
}

.cascader-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.cascader-input:hover {
  border-color: #9ca3af;
}

.cascader-input.active {
  /* border-color: #3b82f6; */
  /* box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); */
}

.cascader-value {
  color: #374151;
  font-size: 0.875rem;
}

.cascader-placeholder {
  color: #9ca3af;
  font-size: 0.875rem;
}

.clear-btn {
  color: #6b7280;
  cursor: pointer;
  padding: 0.125rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  color: #ef4444;
  background: #fef2f2;
}

.cascader-arrow {
  color: #9ca3af;
  transition: transform 0.2s ease;
}

.cascader-arrow.rotated {
  transform: rotate(180deg);
}

.cascader-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 -10px 15px -3px rgba(0, 0, 0, 0.1);
  margin-bottom: 4px;
  max-height: 400px;
  /* overflow: visible; */
  width: fit-content;
}

.cascader-panels {
  display: flex;
  height: 100%;
  max-height: 400px;
}

.cascader-panel {
  flex: 1;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-height: 400px;
}

.cascader-panel:last-child {
  border-right: none;
}

.panel-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  font-size: 0.875rem;
  color: #374151;
  flex-shrink: 0;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  max-height: calc(400px - 60px); /* 减去header的高度 */
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 美化滚动条 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.cascader-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
  color: #374151;
}

.cascader-item:hover {
  background: #f3f4f6;
}

.cascader-item.active {
  background: #eff6ff;
  color: #3b82f6;
}

.item-arrow {
  margin-left: auto;
  color: #9ca3af;
  font-size: 0.75rem;
}

.loading-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
